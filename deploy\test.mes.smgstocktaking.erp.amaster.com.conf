server {
  listen 3444;
  server_name test.erp.smgstocktaking.amaster.com;

  #转发前端的接口
  location / {
    index index.html index.htm;
    root /usr/share/nginx/html;
    try_files $uri $uri/ /smgstocktaking/index.html;

    access_log /var/log/nginx/access_mes_erp.log;
    error_log /var/log/nginx/error_mes_erp.log;
  }

  #转发后台的接口
  location /mes-api/ {
    proxy_pass http://10.1.17.204:60021/mes-api/;
    access_log /var/log/nginx/access_mes_erp.log;
    error_log /var/log/nginx/error_mes_erp.log;
  }
}
