user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    client_max_body_size    25M;
    client_body_buffer_size 128k;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #超时时间设置
    keepalive_timeout  600;
    fastcgi_connect_timeout 600;
    fastcgi_send_timeout 600;
    fastcgi_read_timeout 600;

    proxy_connect_timeout       600;
    proxy_send_timeout          600;
    proxy_read_timeout          600;
    send_timeout                600;

    #gzip  on;
    gzip off; #开启或关闭gzip on off
    gzip_static on; #是否开启gzip静态资源,nginx对于静态文件的处理模块，该模块可以读取预先压缩的gz文件，这样可以减少每次请求进行gzip压缩的CPU资源消耗。该模块启用后，nginx首先检查是否存在请求静态文件的gz结尾的文件，如果有则直接返回该gz文件内容。为了要兼容不支持gzip的浏览器，启用gzip_static模块就必须同时保留原始静态文件和gz文件。这样的话，在有大量静态文件的情况下，将会大大增加磁盘空间。我们可以利用nginx的反向代理功能实现只保留gz文件。
    gzip_disable "msie6"; #不使用gzip IE6
    gzip_min_length 10k; #gzip压缩最小文件大小，超出进行压缩（自行调节）
    gzip_buffers 4 16k; #buffer 不用修改
    gzip_comp_level 5; #压缩级别:1-10，数字越大压缩的越好，时间也越长
    gzip_types text/plain text/css application/xml text/javascript application/x-httpd-php application/json application/css image/jpeg image/gif image/png; # 压缩文件类型
    gzip_vary on; #跟Squid等缓存服务有关，on的话会在Header里增加 "Vary: Accept-Encoding"
    gzip_http_version 1.0; #不设置的话,gzip不生效

    underscores_in_headers on;
    include /etc/nginx/conf.d/*.conf;
}
