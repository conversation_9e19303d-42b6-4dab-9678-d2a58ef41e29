import axios from 'axios'
import { Dialog } from 'vant'
import store from '../store'
import { getToken } from '@/utils/auth'

// 创建axios实例
const service = axios.create({
  // baseURL: process.env.BASE_API, // api 的 base_url
  timeout: 300000 // 请求超时时间 延长为5分钟
})

// request拦截器
service.interceptors.request.use(
  config => {
    if (store.getters.token) {
      config.headers.user_id = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    return config
  },
  error => {
    // Do something with request error
    console.log(error) // for debug
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    // content-type: "application/octet-stream"
    // content-smgstocktaking-encoding: "binary"
    if (response.headers['accept-ranges'] === 'bytes' && response.status === 200) {
      return response
    }
    /**
     * code为非200是抛错 可结合自己业务进行修改
     */
    const res = response.data
    if (res.code !== 200) {
      Dialog.alert({
        title: '出错了',
        message: res.message
      })

      // 50008:非法的token; 50012:其他客户端登录了;  50014:Token 过期了;
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        Dialog.confirm({
          title: '确定登出',
          message: '你已被登出，可以取消继续留在该页面，或者重新登录',
          showConfirmButton: true,
          showCancelButton: true,
          confirmButtonText: '重新登录',
          cancelButtonText: '取消'
        }).then(() => {
          store.dispatch('FedLogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject('error')
    } else {
      return response.data
    }
  },
  error => {
    console.log(error.response) // for debug
    let res = error.response.data
    if (error.response.data.message !== undefined) {
      res = error.response.data.message
    }
    Dialog.alert({
      title: '异常',
      message: res
    })
    return Promise.reject(error)
  }
)

export default service
