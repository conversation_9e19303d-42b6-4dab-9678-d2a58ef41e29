<template>
  <div class="home-container" v-show="(permission || permission2)">
    <van-pull-refresh v-model="pushRefreshLoading" success-text="刷新成功" @refresh="onRefresh">
      <!-- 导航 -->
      <van-nav-bar :title="title" left-text="退出" @click-left="onClickLeft"></van-nav-bar>


      <!-- 新:随行卡号查询图纸 -->
      <van-row>
        <van-cell-group>
          <van-field border label="随行卡号" label-width="60" input-align="center" v-model="cardNoForDrawing"
            class="home-field" placeholder="请输入随行卡号" />
        </van-cell-group>
        <!-- 新:查询图纸按钮 -->
        <van-col span="6">
          <van-button plain hairline type="success" @click="queryDrawingByCardNo">查询图纸</van-button>
        </van-col>
      </van-row>

      <!-- 扫码按钮 -->
      <van-button plain hairline type="primary" @click="scanBarcode">扫码</van-button>
      <video id="video" style="width: 100%; height: 200px; display: none;"></video>


      <!-- 新:图纸显示区域 -->
      <van-row v-if="drawingUrl || drawingGrayUrl" class="home-drawing">
        <van-col span="24">
          <div v-if="khth" style="margin-bottom: 10px;">
            <strong>型号：{{ khth }}</strong>
          </div>
          <div v-if="drawingUrl">
            <strong>详图：</strong><br />
            <iframe
              :src="drawingUrl.startsWith('http') ? drawingUrl : 'http://dev.erp.aag.com:80/attachment' + drawingUrl"
              width="100%" height="300" style="border: none;"></iframe>
          </div>
          <div v-if="drawingGrayUrl" style="margin-top: 10px;">
            <strong>黑白图：</strong><br />
            <iframe
              :src="drawingGrayUrl.startsWith('http') ? drawingGrayUrl : 'http://dev.erp.aag.com:80/attachment' + drawingGrayUrl"
              alt="黑白图" style="width: 100%; max-height: 300px; object-fit: contain;"></iframe>
          </div>
        </van-col>
      </van-row>




      <!-- 遮罩 -->
      <van-overlay class="home-overlay" :show="homeLoading" />
      <!-- 通知 -->
      <van-row>
        <van-notice-bar wrapable :scrollable="false" class="home-notice-bar">
          系统信息：{{ noticeMessge }}
        </van-notice-bar>
      </van-row>


    </van-pull-refresh>
  </div>
</template>

<script>
import { Field, NavBar, Col, CellGroup, Row, Popup, Picker, NoticeBar, Button, Dialog, Overlay, PullRefresh } from 'vant'
import { mapState } from 'vuex'
import { save } from '@/api/home'
import { vibrate } from '@/utils/tool'
import { getDrawingByCardNo } from '@/api/home'
import { BrowserBarcodeReader } from '@zxing/library'

export default {
  name: 'Home',
  components: {
    [Field.name]: Field,
    [NavBar.name]: NavBar,
    [Row.name]: Row,
    [Col.name]: Col,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [CellGroup.name]: CellGroup,
    [NoticeBar.name]: NoticeBar,
    [Dialog.name]: Dialog,
    [Button.name]: Button,
    [Overlay.name]: Overlay,
    [PullRefresh.name]: PullRefresh
  },
  data() {
    return {
      title: '半成品盘点',
      noticeMessge: '',
      // 盘点车间
      pdcj: '',
      // 班别
      bb: '',

      followCardNo: '', // 移交单号
      count: '', // 数量
      list: [], // 数组
      homeLoading: false,
      permission: false,
      permission2: false,
      pushRefreshLoading: false, // 下拉刷新

      cardNoForDrawing: '', // 随行卡号（用于查询图纸）
      drawingUrl: '', // 详图地址
      drawingGrayUrl: '', // 黑白图地址
      khth: '', // 型号
    }
  },
  computed: {
    ...mapState(['copyright'])
  },
  created() {
    const that = this
    that.permission = that.$store.state.user.roles.indexOf('PDA半成品盘点') > -1
    that.permission2 = that.$store.state.user.roles.indexOf('PDA盘点') > -1
    if (!(that.permission || that.permission2)) {
      Dialog.alert({
        title: '警告',
        message: '您没有权限访问，请联系IT部同事处理！'
      }).then(() => {
        that.onClickLeft()
      })
    }
  },
  mounted() {
    const that = this
    that.$nextTick(() => {
      that.$refs.followCardNo.focus()
    })
    that.pdcj = JSON.parse(JSON.stringify(that.$store.state.user.lineCategory)) // 盘点车间
    that.bb = JSON.parse(JSON.stringify(that.$store.state.user.classCategory)) // 班别
  },
  methods: {
    // 退出
    onClickLeft() {
      localStorage.clear()
      sessionStorage.clear()
      const that = this
      this.$store.dispatch('LogOut').then(() => {
        that.$router.push({ path: that.redirect || '/login' })
      })
    },
    followCardNoKeyDown(event) {
      // 点击回车要执行的事件
      if (event.which === 13 || event.keyCode === 13) {
        if (this.followCardNo === '') {
          Dialog.alert({ title: '请输入移交单号！' })
          return
        }
        const that = this
        this.list = []
        this.noticeMessge = ''
        that.homeLoading = true
        // 保存
        const param = {
          pdcj: that.$store.state.user.lineCategory, // 盘点车间
          bb: that.$store.state.user.classCategory, // 班别
          yjd_no: Number(that.followCardNo), // 移交单号
        }
        // 查询
        save(param).then(response => {
          that.homeLoading = false
          const data = response.data
          if (data === undefined || data.length === 0) {
            that.noticeMessge = '保存异常'
            return
          }
          that.noticeMessge = data.message
          if (data.data !== undefined && data.data !== null) {
            that.list.push(data.data)
            vibrate(500)
            that.followCardNo = ''
          }
        }).catch(error => {
          that.homeLoading = false
          that.noticeMessge = error
        })
      }
    },
    // 查询
    searchData() {
      if (this.followCardNo === '') {
        Dialog.alert({ title: '请输入移交单号！' })
        return
      }
      const that = this
      that.homeLoading = true
      that.list = []
      this.noticeMessge = ''

      // 查询
      const param = {
        pdcj: that.$store.state.user.lineCategory, // 盘点车间
        bb: that.$store.state.user.classCategory, // 班别
        yjd_no: Number(that.followCardNo), // 移交单号
      }
      // 查询
      save(param).then(response => {
        that.homeLoading = false
        const data = response.data
        if (data === undefined || data.length === 0) {
          that.noticeMessge = '查询异常'
          return
        }
        that.noticeMessge = data.message
        if (data.data !== undefined && data.data !== null) {
          that.list.push(data.data)
          that.followCardNo = ''
        }
      }).catch(error => {
        that.homeLoading = false
        that.noticeMessge = error
      })
    },




    // 保存
    saveData() {
      if (this.followCardNo === '') {
        Dialog.alert({ title: '请输入移交单号！' })
        return
      }
      if (this.count === '') {
        Dialog.alert({ title: '数量异常！' })
        return
      }

      const that = this
      // 修改
      const param = {
        sl: that.count,
        id: that.list[0].id,
        pdcj: that.$store.state.user.lineCategory,
        bb: that.$store.state.user.classCategory,
        yjd_no: Number(that.followCardNo),
        sign: 'modify',
      }

      that.homeLoading = true
      that.noticeMessge = ''
      // 查询
      save(param).then(response => {
        that.homeLoading = false
        const data = response.data
        if (data === undefined || data.length === 0) {
          that.noticeMessge = '保存异常'
          return
        }
        that.noticeMessge = data.message
        if (data.data !== undefined && data.data !== null && data.data.id !== 0) {
          that.list = []
          that.list.push(data.data)
          vibrate(500)
          that.followCardNo = ''
        } else {
          Dialog.alert({ title: data.message })
        }
        document.getElementById('followCardNo').focus()
      }).catch(() => {
        that.homeLoading = false
        // that.noticeMessge = error
      })
    },
    // 新单
    newData() {
      this.list = []
      this.followCardNo = ''
      this.count = ''
      this.noticeMessge = ''
      document.getElementById('followCardNo').focus()
    },
    // 删除
    removeData() {
      if (this.list === undefined || this.list.length === 0) {
        Dialog.alert({ title: '请先输入移交单号并查询到数据！' })
        return
      }
      const followCardNo = this.list[0].yjd_no
      if (followCardNo === undefined || followCardNo === '') {
        Dialog.alert({ title: '数据异常！' })
        return
      }
      const that = this
      Dialog.confirm({
        title: '你确认要删除移交单号' + followCardNo + '吗？'
      }).then(() => {
        const param = {
          sl: that.count,
          id: that.list[0].id,
          pdcj: that.$store.state.user.lineCategory,
          bb: that.$store.state.user.classCategory,
          yjd_no: Number(that.followCardNo),
          sign: 'delete',
        }
        that.homeLoading = true
        this.noticeMessge = ''
        save(param).then(response => {
          that.homeLoading = false
          const data = response.data
          if (data === undefined || data.length === 0) {
            that.noticeMessge = '删除异常'
            return
          }
          that.noticeMessge = data.message

          // 型号、色号、数量、随行卡号
          that.list = []
          that.count = ''
          that.followCardNo = ''
          document.getElementById('followCardNo').focus()
        }).catch(error => {
          that.homeLoading = false
          that.noticeMessge = error
        })
      })
    },
    cilckRow(row) {
      // 数量、随行卡号
      this.count = row.sl
      this.followCardNo = row.yjd_no
    },
    alert() {
      // Notify({
      //   message: '自定义颜色',
      //   color: '#ad0000',
      //   background: '#ffe1e1'
      // });

      // Notify({
      //   message: '自定义时长',
      //   duration: 1000
      // });
    },
    stopKeyborad(id) {
      const input = document.getElementById(id)
      input.style.background = 'white'
      if (input) {
        input.readOnly = true
        setTimeout(() => {
          input.readOnly = false
        }, 200)
      }
    },
    onBlurField(event) {
      event.target.style.background = '#BEEDC7'
    },
    onRefresh() {
      location.reload()
      setTimeout(() => {
        this.pushRefreshLoading = false
      }, 500)
    },

    // 新：根据随行卡号查询图纸
    async queryDrawingByCardNo() {
      const cardNo = this.cardNoForDrawing.trim();
      if (!cardNo) {
        this.noticeMessge = '请输入随行卡号！';
        this.drawingUrl = '';
        this.drawingGrayUrl = '';
        this.khth = '';
        return;
      }

      this.homeLoading = true;
      this.noticeMessge = '正在查询图纸...';

      try {
        const res = await getDrawingByCardNo(cardNo)
        console.log('API响应数据:', res);

        if (res.code === 200) {
          const data = res.data.data;
          this.khth = data.khth; // 型号
          this.drawingUrl = data.tzmc; // 详图地址
          this.drawingGrayUrl = data.tzmc_gray; // 黑白图地址
          this.noticeMessge = `图纸查询成功！型号：${data.khth}`;
        } else {
          this.khth = '';
          this.drawingUrl = '';
          this.drawingGrayUrl = '';
          this.noticeMessge = res.data.message || '未找到对应图纸';
        }
      } catch (error) {
        this.khth = '';
        this.drawingUrl = '';
        this.drawingGrayUrl = '';
        this.noticeMessge = '查询图纸失败：' + (error.message || '网络错误');
      } finally {
        this.homeLoading = false;
      }
    },

    //扫码方法
    async scanBarcode() {
      try {
        const codeReader = new BrowserBarcodeReader();
        const result = await codeReader.decodeFromInputVideoDevice(undefined, 'video');
        if (result && result.text) {
          this.cardNoForDrawing = result.text.trim();
          this.noticeMessge = '扫码成功，正在查询图纸...';
          await this.queryDrawingByCardNo(); // 自动查询图纸
        } else {
          this.noticeMessge = '未识别到条码，请重试';
        }
      } catch (error) {
        console.error('扫码失败:', error);
        this.noticeMessge = '扫码失败：' + error.message;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  width: 100vw;
  min-height: 100vh;
  background-color: #BEEDC7;

  .home-field {
    background-color: #BEEDC7;
  }

  .home-row-button {
    text-align: center;
    margin-bottom: 5px;
    margin-top: 15px;
  }

  .home-user-bar {
    background-color: #BEEDC7;
  }

  .home-notice-bar {
    background-color: #BEEDC7;
    min-height: 150px;
  }

  .home-table {
    background-color: #BEEDC7;
  }

  .home-overlay {
    z-index: 10 !important;
  }

  .home-drawing {
    margin-top: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
  }
}
</style>
