'use strict'
import request from '@/utils/request'

export function login(username, password) {
  return request({
    url: '/mes-api/common/user/login',
    method: 'post',
    data: {
      username,
      password
    }
  })
}

export function pwdmodify(username, old_pwd, new_pwd, new_pwd2) {
  return request({
    url: '/mes-api/common/user/pwdmodify',
    method: 'post',
    data: {
      username,
      old_pwd,
      new_pwd,
      new_pwd2
    }
  })
}

export function getInfo(token) {
  return request({
    url: '/mes-api/common/user/info?t=' + (new Date()).getTime(),
    method: 'get',
    params: { token }
  })
}

export function logout() {
  return request({
    url: '/mes-api/common/user/logout',
    method: 'post'
  })
}

export function getTree(record_id, mould) {
  return request({
    url: '/role-api/v1/user/menu/tree?record_id=' + record_id + '&mould=' + mould + '&t=' + Math.random(),
    method: 'get'
  })
}

export function refresh() {
  return request({
    url: '/mes-api/common/user/refresh',
    method: 'get'
  })
}

// 获取盘点车间列表信息
export function getInventoryData() {
  return request({
    url: '/mes-api/mes/bcp_pdb/pdcj',
    method: 'get'
  })
}
