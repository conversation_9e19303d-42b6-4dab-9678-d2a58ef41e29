import Vue from 'vue'
import Router from 'vue-router'

import Login from '@/page/Login'
import Home from '@/page/Home'

Vue.use(Router)

export default new Router({
  mode: "history", // 去除#号
  base: "/followcard/", // 开启二级目录访问
  routes: [
    {
      path: "/login",
      name: "Login",
      component: Login,
    },
    {
      path: "/home",
      name: "Home",
      component: Home,
    },
    {
      path: "/",
      name: "Home",
      component: Home,
    },
  ],
});
