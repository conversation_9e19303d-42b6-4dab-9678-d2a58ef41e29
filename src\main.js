import Vue from 'vue'
import App from './App.vue'
import router from './router/index'
import store from './store/index'

import '@/permission' // permission control

import 'xe-utils'
import VXETable from 'vxe-table'
import VXETablePluginElement from 'vxe-table-plugin-element'
import 'vxe-table/lib/index.css'
import 'vxe-table-plugin-element/dist/style.css'
import { SnbTable, SnbTableColumn, SnbTableHeader } from 'vue-snb-table'
// import './utils/vconsoleUtil.js'

Vue.component('snb-table', SnbTable)
Vue.component('snb-table-column', SnbTableColumn)
Vue.component('snb-table-header', SnbTableHeader)
Vue.use(VXETable)
VXETable.use(VXETablePluginElement)

Vue.config.productionTip = false

new Vue({
  store,
  router,
  render: h => h(App)
}).$mount('#app')
