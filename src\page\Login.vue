<template>
  <div class="login-container">
    <van-pull-refresh v-model="pushRefreshLoading" success-text="刷新成功" @refresh="onRefresh">
      <!-- 导航 -->
      <van-nav-bar :title="title" left-text="返回" @click-left="onClickLeft"></van-nav-bar>
      <!-- 图片 -->
      <van-image class="login-logo" fit="contain" :src="logo" />
      <van-row>
        <van-col span="8" class="login-title">半成品盘点</van-col>
        <van-col span="8" class="login-tips">用户登录</van-col>
      </van-row>
      <br />
      <!-- 登录 -->
      <van-cell-group>
        <van-field v-model="loginForm.username" required clearable label="工号" placeholder="请输入工号" class="login-field"
          @keydown="handleKey($event, 0)" id="input_0" @focus="stopKeyborad('input_0')" />
        <van-field v-model="loginForm.password" type="password" label="密码" placeholder="请输入密码" class="login-field"
          required @keydown="handleKey($event, 1)" id="input_1" @focus="stopKeyborad('input_1')" />
        <van-field readonly required clickable label="班别" :value="loginForm.classCategory" placeholder="请选择班别"
          @click="showClassCategoryPicker = true" @focus="showClassCategoryPicker = true; document.activeElement.blur()"
          class="login-field" @keydown="handleKey($event, 2)" id="input_2" />
        <van-popup v-model="showClassCategoryPicker" position="bottom">
          <van-picker show-toolbar :columns="classList" @cancel="showClassCategoryPicker = false"
            @confirm="onConfirmClassCategory" />
        </van-popup>
        <van-field readonly required clickable label="盘点车间" :value="loginForm.lineCategory" placeholder="请选择盘点车间"
          @click="showLineCategoryPicker = true" @focus="showLineCategoryPicker = true" class="login-field"
          @keydown="handleKey($event, 3)" id="input_3" />
        <van-popup v-model="showLineCategoryPicker" position="bottom">
          <van-picker show-toolbar :columns="lineList" @cancel="showLineCategoryPicker = false"
            @confirm="onConfirmLineCategory" />
        </van-popup>
      </van-cell-group>
      <br>
      <van-button type="primary" :loading="loginLoading" loading-text="登录中..." @click="login" size="large"
        id="input_4">登录</van-button>
      <!-- 底部 -->
      <Footer :msg="copyright"></Footer>
      <!-- 遮罩 -->
      <van-overlay class="back-overlay" :show="backLoading" />
    </van-pull-refresh>
  </div>
</template>

<script>
import { Field, NavBar, Image, CellGroup, Icon, Row, Col, Button, Dialog, Loading, Popup, Picker, Overlay, PullRefresh } from 'vant'
import Footer from '@/components/Footer'
import { getInventoryData } from '@/api/login'
import { mapState } from 'vuex'
export default {
  name: 'Login',
  components: {
    Footer,
    [Icon.name]: Icon,
    [NavBar.name]: NavBar,
    [Field.name]: Field,
    [CellGroup.name]: CellGroup,
    [Image.name]: Image,
    [Row.name]: Row,
    [Col.name]: Col,
    [Button.name]: Button,
    [Dialog.name]: Dialog,
    [Loading.name]: Loading,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [Overlay.name]: Overlay,
    [PullRefresh.name]: PullRefresh
  },
  data() {
    return {
      loginForm: {
        username: process.env.NODE_ENV === 'development' ? '100000' : '',
        password: process.env.NODE_ENV === 'development' ? 'kz2011' : '',
        classCategory: process.env.NODE_ENV === 'development' ? '甲班' : '',
        lineCategory: process.env.NODE_ENV === 'development' ? '中转分厂' : ''
      },
      lineList: [],
      classList: ['甲班', '乙班'],
      loginLoading: false,
      showClassCategoryPicker: false,
      showLineCategoryPicker: false,
      logo: require('@/assets/logo.png'),
      title: 'AAG-PDA-半成品盘点',
      backLoading: false,
      pushRefreshLoading: false // 下拉刷新
    }
  },
  computed: {
    ...mapState(['copyright'])
  },
  mounted() {
    document.getElementById('input_0').focus()
    this.init()
  },
  methods: {
    // 初始化数据
    init() {
      // 加载盘点车间列表数据
      getInventoryData().then(response => {
        const data = response.data.data
        if (data === undefined || data.length === 0) {
          Dialog.alert({ title: '加载盘点车间数据异常' })
          return
        }
        const workLocationMap = {}
        const workLocationList = []
        for (let i = 0; i < data.length; i++) {
          if (workLocationMap[data[i].description] === undefined) {
            workLocationMap[data[i].description] = data[i].description
            workLocationList.push(data[i].description)
          }
        }
        this.lineList = JSON.parse(JSON.stringify(workLocationList))
      }).catch(error => {
        reject(error)
      })
    },
    handleKey($event, index) {
      // 点击回车要执行的事件
      if (event.which === 13 || event.keyCode === 13) {
        const this_id = 'input_' + index
        const e = document.getElementById(this_id)
        if (e) {
          e.blur()
        }
        if (index === 2) {
          this.onConfirmClassCategory(this.loginForm.classCategory)
        } else if (index === 3) {
          this.onConfirmLineCategory(this.loginForm.lineCategory)
        }
        setTimeout(function () {
          const next_id = 'input_' + (index + 1)
          const e = document.getElementById(next_id)
          if (e) {
            e.focus()
          }
        }, 300)
      }
    },
    onConfirmClassCategory(value) {
      this.loginForm.classCategory = value
      this.showClassCategoryPicker = false
    },
    onConfirmLineCategory(value) {
      this.loginForm.lineCategory = value
      this.showLineCategoryPicker = false
    },
    login() {
      if (this.loginForm.username === '') {
        Dialog.alert({
          title: '请输入登录工号！'
        })
        return
      }
      if (this.loginForm.password === '') {
        Dialog.alert({
          title: '请输入密码！'
        })
        return
      }
      if (this.loginForm.classCategory === '') {
        Dialog.alert({
          title: '请输入班别！'
        })
        return
      }
      if (this.loginForm.lineCategory === '') {
        Dialog.alert({
          title: '请选择盘点车间！'
        })
        return
      }
      const that = this
      that.loginLoading = true
      // that.loginForm.classCategory = that.$route.query.c
      that.loginForm.username = parseInt(that.loginForm.username)
      that.$store.dispatch('Login', that.loginForm).then((data) => {
        // Dialog.alert({
        //   title: '登录成功'
        // }).then(function() {
        //   that.loginLoading = false
        // })
        that.loginLoading = false
        that.$router.push({ path: that.redirect || '/home' })
      }).catch((err) => {
        console.log(err)
        that.loginLoading = false
      })
    },
    stopKeyborad(id) {
      const input = document.getElementById(id)
      if (input) {
        input.readOnly = true
        setTimeout(() => {
          input.readOnly = false
        }, 200)
      }
    },
    // 退出
    onClickLeft() {
      localStorage.clear()
      sessionStorage.clear()
      let path = '/mobile'
      if (process.env.NODE_ENV === 'development') {
        path = ':9020' + path
      }
      this.backLoading = true
      window.location.href = process.env.VUE_APP_URL_PREFIX + path
    },
    onRefresh() {
      location.reload()
      setTimeout(() => {
        this.pushRefreshLoading = false
      }, 500)
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  width: 100vw;
  min-height: 100vh;
  background-color: #BEEDC7;

  .login-logo {
    width: 100%;
    height: 5rem;
    // margin-left: 0.7rem;
  }

  .login-title {
    width: 95%;
    font-size: 2rem;
    text-align: center;
  }

  .login-tips {
    // margin-top: 1rem;
    width: 95%;
    font-size: 1rem;
    text-align: center;
  }

  .login-field {
    background-color: #BEEDC7;
  }

  .back-overlay {
    z-index: 10 !important;
  }
}
</style>
