import Vue from 'vue'
import Vuex from 'vuex'

// 引入全局存储
import * as actions from './actions'
import * as mutations from './mutations'
import getters from './getters'
import user from './modules/user'

// 引入模块存储
import home from './modules/home'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    copyright: 'Powered By AAG ©2019'
  },
  actions,
  mutations,
  getters,
  modules: {
    home,
    user
  }
})
