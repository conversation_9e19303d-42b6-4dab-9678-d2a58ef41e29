
---
apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: mes-pda-smgstocktaking-dp
  namespace: amaster-apps
  labels:
    k8s-app: mes-pda-smgstocktaking
spec:
  replicas: 2 #最大po数量
  revisionHistoryLimit: 10
  minReadySeconds: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:  ##由于replicas为2,则整个升级,pod个数在1-3个之间
      maxSurge: 1     #滚动升级时会先启动1个pod
      maxUnavailable: 0 #滚动升级时允许的最大Unavailable的pod个数
  template:
    metadata:
      labels:
        k8s-app: mes-pda-smgstocktaking
    spec:
      nodeSelector:
        amaster_prefer: web  #指定调度节点为带有label标记为：amaster_prefer=web的node节点
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: amaster_prefer
                      operator: In
                      values:
                        - web
                topologyKey: kubernetes.io/hostname
      containers:
        - image: harbor.amaster.com/amaster/pro_pda_smgstocktaking_erp_pda:v0.11.%subversion%
          imagePullPolicy: Always
          name: mes-pda-smgstocktaking
          ports:
            - containerPort: 3444
              protocol: TCP
          resources:
            requests:
              cpu: 100m
              memory: 100Mi
            limits:
              cpu: 100m
              memory: 100Mi
          livenessProbe:
            tcpSocket:
              port: 3444
            initialDelaySeconds: 5
            timeoutSeconds: 15
          readinessProbe:
            httpGet:
              path: /favicon.ico
              port: 3444
            initialDelaySeconds: 8
            timeoutSeconds: 15

---
apiVersion: v1
kind: Service
metadata:
  name: mes-pda-smgstocktaking-svc
  namespace: amaster-apps
  labels:
    k8s-app: mes-pda-smgstocktaking
spec:
  ports:
    - name: api
      port: 3444
      protocol: TCP
      targetPort: 3444
  selector:
    k8s-app: mes-pda-smgstocktaking
  type: NodePort
