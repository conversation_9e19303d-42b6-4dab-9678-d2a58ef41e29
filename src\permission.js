import router from './router'
import store from './store'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { Dialog } from 'vant'
import { getToken } from '@/utils/auth' // getToken from cookie

NProgress.configure({ showSpinner: false })// NProgress configuration

const whiteList = ['/login'] // 不重定向白名单
router.beforeEach(async(to, from, next) => {
  NProgress.start()
  if (getToken()) {
    if (to.path === '/login') {
      next({ path: '/' })
      // eslint-disable-next-line no-tabs
      NProgress.done() // if current page is dashboard will not trigger	afterEach hook, so manually handle it
    } else {
      try {
        // 加载用户角色
        if (store.getters.roles.length === 0) {
          await store.dispatch('getInfo')
        }
        if (store.getters.id === '') {
          // 加载用户token
          await store.dispatch('getInfo')
          next({ ...to, replace: true })
        } else {
          next()
        }
      } catch (error) {
        // remove token and go to login page to re-login
        // await store.dispatch('user/resetToken')
        Dialog.alert({
          title: '出错了',
          message: error || 'Has Error'
        })
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      const redirectPath = `/login?redirect=${to.path}`
      // if (sessionStorage.length > 0) {
      //   const bb = sessionStorage.getItem('jh')
      //   if (bb !== undefined && bb !== '') {
      //     redirectPath = redirectPath + '&c=' + bb
      //     // sessionStorage.clear()
      //   }
      // }
      next(redirectPath) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done() // 结束Progress
})
