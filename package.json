{"name": "smgstocktaking", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 9210 --mode development", "build": "vue-cli-service build", "dev": "vue-cli-service build --mode dev", "test": "vue-cli-service build --mode test", "prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.0", "core-js": "^2.6.5", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "nprogress": "^0.2.0", "vant": "^2.2.15", "vconsole": "^3.3.4", "vue": "^2.6.10", "vue-barcode-reader": "^1.0.3", "vue-router": "^3.1.3", "vue-snb-table": "^2.6.6", "vuex": "^3.1.2", "vxe-table": "^2.9.6", "vxe-table-plugin-element": "^1.8.7", "xe-utils": "^2.4.5"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.0", "@vue/cli-plugin-eslint": "^3.12.0", "@vue/cli-service": "^3.12.0", "babel-eslint": "^10.0.1", "babel-plugin-import": "^1.13.0", "compression-webpack-plugin": "^3.1.0", "cssnano": "^4.1.10", "cssnano-preset-advanced": "^4.0.7", "eslint": "^5.16.0", "eslint-config-standard": "^14.1.0", "eslint-friendly-formatter": "^4.0.1", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^10.0.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^5.0.0", "postcss-aspect-ratio-mini": "^1.0.1", "postcss-cssnext": "^3.1.0", "postcss-import": "^12.0.1", "postcss-px-to-viewport": "^1.1.1", "postcss-url": "^8.0.0", "postcss-viewport-units": "^0.1.6", "postcss-write-svg": "^3.0.1", "sass": "^1.54.4", "sass-loader": "^8.0.0", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}