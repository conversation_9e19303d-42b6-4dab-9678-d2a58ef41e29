'use strict'
import request from '@/utils/request'

// 获取日期
export function getDate() {
  return request({
    url: '/mes-api/pda/pfsj/date',
    method: 'get'
  })
}

// PDA保存信息
export function save(data) {
  return request({
    url: '/mes-api/mes/bcp_pdb/pdasave',
    method: 'post',
    data
  })
}

export function getSelection(system, type, condition) {
  return request({
    url: '/mes-api/config/simpleconfig/getlist?system=' + system + '&type=' + type + '&condition=' + encodeURIComponent(condition) + '&pagesign=true',
    method: 'get'
  })
}

// 随行卡获取图纸
export function getDrawingByCardNo(cardNo) {
  return request({
    url: '/mes-api/mes/cardno/mjtzinfo',
    method: 'get',
    params: { card_no: cardNo }
  })
}
