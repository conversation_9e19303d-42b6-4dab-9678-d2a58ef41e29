import { login, logout, getInfo, getTree, refresh, pwdmodify } from '@/api/login'
import { getToken, setToken, removeToken, getClassCategory, setClassCategory, removeClassCategory, getLineCategory, setLineCategory, removeLineCategory } from '@/utils/auth'
import md5 from 'js-md5'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    id: '',
    record_id: '',
    menu_tree: [],
    system: 'mes',
    old_pwd: '',
    new_pwd: '',
    new_pwd2: '',
    classCategory: getClassCategory(),
    lineCategory: getLineCategory()
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_CLASS_CATEGORY: (state, classCategory) => {
      state.classCategory = classCategory
    },
    SET_LINE_CATEGORY: (state, lineCategory) => {
      state.lineCategory = lineCategory
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_USERID: (state, id) => {
      state.id = id
    },
    SET_RECORDID: (state, record_id) => {
      state.record_id = record_id
    },
    SET_MENU_TREE: (state, menu_tree) => {
      state.menu_tree = menu_tree
    },
    // 班别
    MODIFY_CLASS_CATEGORY: (state, value) => {
      state.classCategory = value
    },
    // 线别
    MODIFY_LINE_CATEGORY: (state, value) => {
      state.lineCategory = value
    }
  },

  actions: {
    // 班别
    modifyClassCategory({ commit }, value) {
      commit('MODIFY_CLASS_CATEGORY', value)
    },
    // 线别
    modifyLineCategory({ commit }, value) {
      commit('MODIFY_LINE_CATEGORY', value)
    },

    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username
      const password = md5(userInfo.password)

      return new Promise((resolve, reject) => {
        login(username, password).then(response => {
          const data = response.data
          // 写入cookie
          setToken(data.token)
          setClassCategory(userInfo.classCategory)
          setLineCategory(userInfo.lineCategory)
          // 写入上下文
          commit('SET_TOKEN', data.token)
          commit('SET_CLASS_CATEGORY', userInfo.classCategory)
          commit('SET_LINE_CATEGORY', userInfo.lineCategory)
          user.token = data.token
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 密码修改
    PwdModify({ commit }, userInfo) {
      const username = userInfo.username
      const old_pwd = md5(userInfo.old_pwd)
      const new_pwd = md5(userInfo.new_pwd)
      const new_pwd2 = md5(userInfo.new_pwd2)
      return new Promise((resolve, reject) => {
        pwdmodify(username, old_pwd, new_pwd, new_pwd2).then(response => {
          commit('SET_TOKEN', '')
          removeToken()
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    getInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token).then(response => {
          const data = response.data
          // console.log(data)
          if (data.roles && data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', data.roles)
          }
          // else {
          //   reject('getInfo: roles must be a non-null array !')
          // }
          commit('SET_USERID', data.id)
          commit('SET_RECORDID', data.record_id)
          commit('SET_NAME', data.name)
          commit('SET_CLASS_CATEGORY', getClassCategory())
          commit('SET_LINE_CATEGORY', getLineCategory())
          // commit('SET_CLASS_CATEGORY', data.classCategory)
          resolve(response)
        }).catch(error => {
          // 清除token
          if (error.response.status === 403) {
            state.token = ''
            setToken(state.token)
            commit('SET_TOKEN', state.token)
            state.classCategory = ''
            setClassCategory(state.classCategory)
            commit('SET_CLASS_CATEGORY', state.classCategory)
            state.lineCategory = ''
            setLineCategory(state.lineCategory)
            commit('SET_LINE_CATEGORY', state.lineCategory)
          }
          reject(error)
        })
      })
    },

    // 获取用户信息
    getMenuTree({ commit, state }) {
      return new Promise((resolve, reject) => {
        getTree(state.record_id, state.system).then(response => {
          const data = response.data
          commit('SET_MENU_TREE', data)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // remove token
    resetToken({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resolve()
      })
    },

    // 登出
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          removeToken()
          removeClassCategory()
          removeLineCategory()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 刷新权限
    Refresh({ commit, state }) {
      return new Promise((resolve, reject) => {
        refresh(state.token).then(response => {
          const data = response.data
          setToken(data.token)
          user.token = data.token
          commit('SET_TOKEN', data.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        commit('SET_CLASS_CATEGORY', '')
        commit('SET_LINE_CATEGORY', '')
        removeClassCategory()
        removeLineCategory()
        resolve()
      })
    }
  }
}

export default user
