import Cookies from 'js-cookie'

const TokenKey = 'amaster_mes_materialoutboundreceipt_token'
const ClassCategoryKey = 'amaster_mes_materialoutboundreceipt_class'
const LineCategoryKey = 'amaster_mes_materialoutboundreceipt_line'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getClassCategory() {
  return Cookies.get(ClassCategoryKey)
}

export function removeClassCategory() {
  return Cookies.remove(ClassCategoryKey)
}

export function setClassCategory(token) {
  return Cookies.set(ClassCategoryKey, token)
}

export function getLineCategory() {
  return Cookies.get(LineCategoryKey)
}

export function removeLineCategory() {
  return Cookies.remove(LineCategoryKey)
}

export function setLineCategory(token) {
  return Cookies.set(<PERSON><PERSON>ategory<PERSON><PERSON>, token)
}
